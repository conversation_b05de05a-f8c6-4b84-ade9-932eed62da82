"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Upload, FileText, Download, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface CsvImportDialogProps {
  projectId: string;
  onImportComplete: () => void;
}

type ImportFormat = "sermush-links" | "ahrefs" | "custom";

interface ParsedDiscoveredLink {
  url: string;
  title: string;
  source_url: string;
  source_title: string;
  anchor_text: string;
  link_type: string;
  dr_score?: number;
  traffic?: number;
  is_nofollow: boolean;
  is_sponsored: boolean;
  first_seen?: Date;
  last_seen?: Date;
}

interface ParseProgress {
  stage: 'reading' | 'parsing' | 'uploading' | 'complete';
  current: number;
  total: number;
  validLinks: ParsedDiscoveredLink[];
  errors: string[];
}

export function CsvImportDialog({ projectId, onImportComplete }: CsvImportDialogProps) {
  const [open, setOpen] = useState(false);
  const [format, setFormat] = useState<ImportFormat>("sermush-links");
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [progress, setProgress] = useState<ParseProgress | null>(null);

  const formatOptions = [
    { value: "sermush-links", label: "sermush Links Export" },
    // { value: "ahrefs", label: "Ahrefs Backlinks Export" },
    // { value: "custom", label: "Custom CSV Format" },
  ];

  const formatDescriptions = {
    "sermush-links": "CSV format from Semrush Links with columns: Page ascore, Source title, Source url, Target url, Anchor, etc. This will import backlinks and update domain statistics in the all_links table.",
    "ahrefs": "CSV export from Ahrefs backlinks report with standard Ahrefs columns. These will be imported as discovered backlinks to your project.",
    "custom": "Custom CSV format - use the template below for proper formatting. These will be imported as discovered backlinks to your project."
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === "text/csv") {
      setFile(selectedFile);
    } else {
      toast.error("Please select a valid CSV file");
    }
  };

  const parseCSVRow = (row: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < row.length; i++) {
      const char = row[i];
      
      if (char === '"' && (i === 0 || row[i-1] === ',')) {
        inQuotes = true;
      } else if (char === '"' && inQuotes && (i === row.length - 1 || row[i+1] === ',')) {
        inQuotes = false;
      } else if (char === ',' && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current);
    return result;
  };

  const parseDate = (dateString: string): Date | null => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  };

  const parseLinkByFormat = (rowData: Record<string, string>, format: string): ParsedDiscoveredLink | null => {
    switch (format) {
      case 'sermush-links':
        return parsesermushLinksFormat(rowData);
      case 'ahrefs':
        return parseAhrefsFormat(rowData);
      case 'custom':
        return parseCustomFormat(rowData);
      default:
        return null;
    }
  };

  const parsesermushLinksFormat = (data: Record<string, string>): ParsedDiscoveredLink | null => {
    const sourceUrl = data['Source url'];
    const targetUrl = data['Target url'];
    
    if (!sourceUrl || !targetUrl) {
      return null;
    }

    return {
      url: sourceUrl, // 外部网站URL (发现的外链来源)
      title: data['Source title'] || '',
      source_url: targetUrl, // 项目网站URL (被链接的目标)
      source_title: data['Source title'] || '',
      anchor_text: data['Anchor'] || '',
      link_type: 'backlink',
      dr_score: parseInt(data['Page ascore']) || undefined,
      traffic: parseInt(data['External links']) || undefined,
      is_nofollow: data['Nofollow']?.toLowerCase() === 'true',
      is_sponsored: data['Sponsored']?.toLowerCase() === 'true',
      first_seen: parseDate(data['First seen']),
      last_seen: parseDate(data['Last seen']),
    };
  };

  const parseAhrefsFormat = (data: Record<string, string>): ParsedDiscoveredLink | null => {
    const sourceUrl = data['URL'];
    const targetUrl = data['Target URL'];
    
    if (!sourceUrl || !targetUrl) {
      return null;
    }

    return {
      url: targetUrl, // The target URL is what we're tracking backlinks to
      title: data['Title'] || '',
      source_url: sourceUrl, // The source URL is where the backlink comes from
      source_title: data['Title'] || '',
      anchor_text: data['Anchor text'] || '',
      link_type: 'backlink',
      dr_score: parseInt(data['DR']) || undefined,
      traffic: parseInt(data['Traffic']) || undefined,
      is_nofollow: data['Nofollow']?.toLowerCase() === 'true',
      is_sponsored: data['Sponsored']?.toLowerCase() === 'true',
      first_seen: parseDate(data['First seen']),
      last_seen: parseDate(data['Last seen']),
    };
  };

  const parseCustomFormat = (data: Record<string, string>): ParsedDiscoveredLink | null => {
    const sourceUrl = data['source_url'];
    const targetUrl = data['target_url'];
    
    if (!sourceUrl || !targetUrl) {
      return null;
    }

    return {
      url: targetUrl, // The target URL is what we're tracking backlinks to
      title: data['source_title'] || '',
      source_url: sourceUrl, // The source URL is where the backlink comes from
      source_title: data['source_title'] || '',
      anchor_text: data['anchor_text'] || '',
      link_type: 'backlink',
      dr_score: parseInt(data['dr_score']) || undefined,
      traffic: parseInt(data['traffic']) || undefined,
      is_nofollow: data['is_nofollow']?.toLowerCase() === 'true',
      is_sponsored: data['is_sponsored']?.toLowerCase() === 'true',
      first_seen: parseDate(data['first_seen']),
      last_seen: parseDate(data['last_seen']),
    };
  };

  const handleImport = async () => {
    if (!file) {
      toast.error("Please select a CSV file");
      return;
    }

    setImporting(true);
    setProgress({ stage: 'reading', current: 0, total: 0, validLinks: [], errors: [] });

    try {
      // Stage 1: Read and parse CSV
      const csvContent = await file.text();
      const lines = csvContent.trim().split('\n');
      
      if (lines.length < 2) {
        toast.error("CSV file must contain at least a header and one data row");
        return;
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const dataRows = lines.slice(1);
      
      setProgress(prev => ({ ...prev!, stage: 'parsing', total: dataRows.length }));

      // Stage 2: Parse rows
      const validLinks: ParsedDiscoveredLink[] = [];
      const errors: string[] = [];

      for (let i = 0; i < dataRows.length; i++) {
        try {
          const row = parseCSVRow(dataRows[i]);
          if (row.length < headers.length) {
            errors.push(`Row ${i + 2}: Incomplete data (${row.length}/${headers.length} columns)`);
            continue;
          }

          const rowData = Object.fromEntries(
            headers.map((header, index) => [header, row[index]?.trim().replace(/"/g, '') || ''])
          );

          const parsedLink = parseLinkByFormat(rowData, format);
          if (parsedLink) {
            validLinks.push(parsedLink);
          } else {
            errors.push(`Row ${i + 2}: Invalid or missing required fields`);
          }
        } catch (error) {
          errors.push(`Row ${i + 2}: ${error}`);
        }

        // Update progress every 10 rows
        if (i % 10 === 0 || i === dataRows.length - 1) {
          setProgress(prev => ({ ...prev!, current: i + 1, validLinks: [...validLinks], errors: [...errors] }));
          // Allow UI to update
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      if (validLinks.length === 0) {
        toast.error("No valid links found in CSV file");
        return;
      }

      // Stage 3: Upload to server
      setProgress(prev => ({ ...prev!, stage: 'uploading', current: 0, total: validLinks.length }));

      // 根据格式选择不同的 API 端点
      const apiEndpoint = format === 'sermush-links' 
        ? "/api/discovered-links/semrush-import"
        : "/api/discovered-links/batch-import";

      let requestBody;
      
      if (format === 'sermush-links') {
        // Semrush 格式需要特殊处理
        requestBody = {
          projectId,
          links: validLinks.map(link => ({
            page_ascore: link.dr_score || 0,
            source_title: link.title || '',
            source_url: link.url, // 外部网站URL
            target_url: link.source_url, // 项目网站URL
            anchor: link.anchor_text || '',
            external_links: link.traffic || 0,
            internal_links: 0,
            nofollow: link.is_nofollow || false,
            sponsored: link.is_sponsored || false,
            ugc: false,
            text: true,
            frame: false,
            form: false,
            image: false,
            sitewide: false,
            first_seen: link.first_seen?.toISOString() || new Date().toISOString(),
            last_seen: link.last_seen?.toISOString() || new Date().toISOString(),
            new_link: false,
            lost_link: false
          }))
        };
      } else {
        // 其他格式使用原有的数据结构
        requestBody = {
          projectId,
          links: validLinks.map(link => ({
            url: link.url,
            title: link.title,
            source_url: link.source_url,
            source_title: link.source_title,
            anchor_text: link.anchor_text,
            dr_score: link.dr_score,
            traffic: link.traffic,
            is_nofollow: link.is_nofollow,
            is_sponsored: link.is_sponsored,
            first_seen: link.first_seen?.toISOString(),
            last_seen: link.last_seen?.toISOString()
          }))
        };
      }

      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (response.ok) {
        setProgress(prev => ({ ...prev!, stage: 'complete' }));
        
        // 根据格式显示不同的成功消息
        if (format === 'sermush-links') {
          toast.success(`Semrush import completed! Processed ${validLinks.length} backlinks. ${result.newLinks || 0} new backlinks imported.`);
          if (result.domainStatsUpdated) {
            toast.success(`Updated statistics for ${result.domainStatsUpdated} domains in all_links table.`);
          }
        } else {
          toast.success(`Import completed! Processed ${validLinks.length} backlinks. ${result.newLinks || 0} new backlinks imported.`);
        }
        
        if (result.errors && result.errors.length > 0) {
          console.log('Import errors:', result.errors);
          toast.warning(`${result.errors.length} backlinks had errors and were skipped.`);
        }
        
        if (result.domainStatsErrors && result.domainStatsErrors.length > 0) {
          console.log('Domain stats errors:', result.domainStatsErrors);
          toast.warning(`${result.domainStatsErrors.length} domain statistics updates failed.`);
        }
        
        if (result.debug) {
          console.log('Import debug info:', result.debug);
        }
        
        setOpen(false);
        setFile(null);
        setProgress(null);
        onImportComplete();
      } else {
        console.error('Import failed:', result);
        toast.error(result.error || "Failed to import CSV");
      }
    } catch (error) {
      console.error("Error importing CSV:", error);
      toast.error("Failed to import CSV");
    } finally {
      setImporting(false);
    }
  };

  const downloadTemplate = () => {
    const templates = {
      "sermush-links": `Page ascore,Source title,Source url,Target url,Anchor,External links,Internal links,Nofollow,Sponsored,Ugc,Text,Frame,Form,Image,Sitewide,First seen,Last seen,New link,Lost link
8,JitHub程序员 - 即刻App,https://m.okjike.com/topics/55e02198dcef9f0e00d7b3c3,https://card.hekmon.com/,card.hekmon.com,6,10,FALSE,FALSE,FALSE,TRUE,FALSE,FALSE,FALSE,FALSE,2025-05-05,2025-06-04,FALSE,TRUE
7,不务正业小胡同学的个人主页 - 即刻App,http://m.okjike.com/users/4E822130-4B7B-4DB9-A4CA-B326397ADB32,https://card.hekmon.com/,card.hekmon.com,14,22,FALSE,FALSE,FALSE,TRUE,FALSE,FALSE,FALSE,FALSE,2025-05-01,2025-06-04,FALSE,TRUE`,
      "ahrefs": `Domain,URL,Title,Target URL,Anchor text,DR,UR,Traffic,First seen,Last seen,Nofollow,Sponsored
example.com,https://example.com/page,Example Page Title,https://yourdomain.com,your domain,60,45,1000,2025-04-13,2025-05-04,FALSE,FALSE`,
      "custom": `source_url,source_title,target_url,anchor_text,dr_score,traffic,first_seen,last_seen,is_nofollow,is_sponsored
https://example.com,Example Source Site,https://yourdomain.com,your domain name,60,1000,2025-04-13,2025-05-04,false,false`
    };

    const content = templates[format];
    const blob = new Blob([content], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${format}-template.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Upload className="h-4 w-4 mr-2" />
          Import Backlinks
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Import Discovered Backlinks from CSV</DialogTitle>
          <DialogDescription>
            Import backlink data from sermush Links, Ahrefs, or custom CSV format. These will be added to your discovered backlinks for this project.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {!importing ? (
            <>
              <div className="space-y-2">
                <Label htmlFor="format">Import Format</Label>
                <Select value={format} onValueChange={(value: ImportFormat) => setFormat(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select import format" />
                  </SelectTrigger>
                  <SelectContent>
                    {formatOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  {formatDescriptions[format]}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="file">CSV File</Label>
                <Input
                  id="file"
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className="cursor-pointer"
                />
                {file && (
                  <p className="text-sm text-green-600">
                    Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadTemplate}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download Template
                </Button>
                
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleImport} disabled={!file || importing}>
                    {importing ? "Starting..." : "Import"}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="space-y-4">
              {/* Progress Display */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  {progress?.stage === 'complete' ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                  )}
                  <span className="font-medium">
                    {progress?.stage === 'reading' && 'Reading CSV file...'}
                    {progress?.stage === 'parsing' && 'Parsing CSV data...'}
                    {progress?.stage === 'uploading' && 'Uploading to server...'}
                    {progress?.stage === 'complete' && 'Import completed!'}
                  </span>
                </div>

                {progress && progress.total > 0 && (
                  <div className="space-y-2">
                    <Progress 
                      value={(progress.current / progress.total) * 100} 
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>
                        {progress.stage === 'parsing' && `Processed ${progress.current} of ${progress.total} rows`}
                        {progress.stage === 'uploading' && `Uploading ${progress.validLinks.length} valid backlinks`}
                      </span>
                      <span>{Math.round((progress.current / progress.total) * 100)}%</span>
                    </div>
                  </div>
                )}

                {progress && progress.validLinks.length > 0 && (
                  <div className="text-sm space-y-1">
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span>Found {progress.validLinks.length} valid backlinks</span>
                    </div>
                  </div>
                )}

                {progress && progress.errors.length > 0 && (
                  <div className="text-sm space-y-1">
                    <div className="flex items-center gap-2 text-amber-600">
                      <AlertCircle className="h-4 w-4" />
                      <span>{progress.errors.length} rows with errors (will be skipped)</span>
                    </div>
                    {progress.errors.slice(0, 3).map((error, index) => (
                      <div key={index} className="text-xs text-muted-foreground ml-6">
                        {error}
                      </div>
                    ))}
                    {progress.errors.length > 3 && (
                      <div className="text-xs text-muted-foreground ml-6">
                        ... and {progress.errors.length - 3} more errors
                      </div>
                    )}
                  </div>
                )}
              </div>

              {progress?.stage === 'complete' && (
                <div className="flex justify-end">
                  <Button onClick={() => setOpen(false)}>
                    Close
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}